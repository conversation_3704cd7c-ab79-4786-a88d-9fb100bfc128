<template>
  <a-modal v-model:open="open" width="800px" :maskClosable="false" @ok="handleOk" @cancel="handleCancel" :destroyOnClose="true">
    <template #title>
      <span class="text-14px c-[#333] pt-14px pb-14px"><span>选择部门</span></span>
    </template>
    <template #footer>
      <div class="pt-32px flex justify-end">
        <a-button type="default" @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk" :disabled="selectedDepartments.length === 0">确定</a-button>
      </div>
    </template>
    <div class="h-500px m--24px pt-20px flex">
      <!-- 左侧区域 -->
      <div class="w-350px flex flex-col border border-solid border-[#f0f0f0]">
        <!-- 搜索框 -->
        <div class="p-16px border-b" style="background-color: #fafafb; border-top: 1px solid rgb(***********); border-bottom: 1px solid rgb(***********)">
          <a-input v-model:value="searchText" placeholder="输入查找项" allow-clear>
            <template #suffix>
              <search-outlined class="c-[#bfbfbf] w-16px h-16px" />
            </template>
          </a-input>
        </div>

        <!-- 树形控件 -->
        <div class="w-full flex-1 overflow-y-auto overflow-x-hidden">
          <a-tree
            v-if="!searchText"
            v-model:expandedKeys="expandedKeys"
            v-model:selectedKeys="selectedKeys"
            checkStrictly
            :tree-data="treeData"
            :field-names="{ title: 'department_name', key: 'id', children: 'childs' }"
            checkable
            v-model:checkedKeys="checkedKeys"
            @check="onCheck"
            class="custom-tree"
          />
          <div v-else class="p-16px flex w-full flex-1 flex-col">
            <div class="mb-12px rounded-4px px-12px py-10px bg-#F7F8FA gap-6px flex flex-col justify-between" v-for="item in filterData" :key="item.id">
              <div class="gap-8px flex items-center">
                <a-checkbox v-model:checked="item.check" @change="changeByFilter(item)"></a-checkbox>
                <div class="c-[#333] w-full overflow-hidden text-ellipsis whitespace-nowrap">
                  {{ item.department_name }}
                </div>
              </div>
              <div class="text-11px c-[#999] overflow-hidden text-ellipsis whitespace-nowrap">
                {{ item.company_name }}
              </div>
            </div>
          </div>
        </div>

        <!-- 展开全部 -->
        <div class="p-5px flex justify-center" style="border-top: 1px solid #f0f0f0">
          <span class="text-13px c-[#666] flex cursor-pointer items-center" @click="expandAll" v-if="expandedKeys.length == 0">
            <DoubleRightOutlined class="text-12px mr-4px rotate-90 transform" />

            展开全部
          </span>
          <span class="text-13px c-[#666] flex cursor-pointer items-center" @click="cancelExpand" v-else>
            <DoubleRightOutlined class="text-12px mr-4px rotate-270 transform" />

            取消展开
          </span>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="p-16px flex h-full flex-1 flex-col overflow-auto bg-[#fffff]" style="border-top: 1px solid rgb(***********); border-bottom: 1px solid rgb(***********)">
        <div class="mb-16px flex items-center justify-between">
          <!-- <div class="text-12px c-[#999]">拖拽可调整部门优先级，首个为主部门</div> -->
          <!-- <a-button type="link" @click="handleClear" class="text-12px w-46px">清空</a-button> -->
        </div>
        <div class="selected-departments h-full" id="departments-drag" v-show="selectedDepartments.length > 0">
          <div v-for="(item, index) in selectedDepartments" :key="item.id" class="p-8px-12px mb-12px rounded-4px px-12px py-10px bg-#F7F8FA flex w-full items-center justify-between">
            <div class="gap-2px flex w-full flex-1 flex-col" style="width: calc(100% - 20px)">
              <div class="gap-8px flex items-center">
                <!-- <div class="drag cursor-pointer"><HolderOutlined /></div> -->
                <!-- <drag-outlined class="drag-handle c-[#bfbfbf] cursor-move" /> -->
                <span class="c-[#333] overflow-hidden text-ellipsis whitespace-nowrap" style="max-width: calc(100% - 100px)">
                  {{ item.department_name }}
                </span>
                <span v-if="false && index === 0" style="background: rgb(10 89 231 / 10%)" class="ml-4px text-12px py-2px px-10px c-[#1890ff]">主部门</span>
              </div>
              <div class="text-11px c-[#999] overflow-hidden text-ellipsis whitespace-nowrap">
                {{ item.company_name }}
              </div>
            </div>
            <close-outlined class="c-[#bfbfbf] hover:c-[#999] cursor-pointer" @click="removeSelected(item)" />
          </div>
        </div>
        <div v-if="selectedDepartments.length === 0" class="flex h-full w-full items-center justify-center">
          <a-empty :image="simpleImage">
            <template #description>
              <span class="text-12px">暂无内容，请选择部门</span>
            </template>
          </a-empty>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { Empty } from 'ant-design-vue'

import { SearchOutlined, CloseOutlined, DoubleRightOutlined } from '@ant-design/icons-vue'
import type { TreeProps } from 'ant-design-vue'
import Sortable from 'sortablejs'
import { cloneDeep } from '@/utils'
// import { container, EmptyImage, expand } from '@/assets/images/container.png'
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE
const treeData = ref<any[]>([])

const emit = defineEmits(['change', 'close'])

const open = ref(false)
const searchText = ref('')
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const checkedKeys = ref<any>({
  checked: [],
})
const selectedDepartments = ref<any[]>([])

const filterData = computed(() => {
  const filterSearchData = (data: any, text: any, items: Array<any>) => {
    const arr = items
    if (data.length === 0) {
      return []
    }
    data.forEach((item: any) => {
      // 确保 department_name 存在且不为空
      const departmentName = item.department_name || item.name || ''
      if (departmentName.toLowerCase().includes(text.toLowerCase())) {
        // 为搜索结果添加 check 属性，用于控制复选框状态
        const searchItem = {
          ...item,
          check: checkedKeys.value.checked.includes(item.id),
        }
        arr.push(searchItem)
      }
      if (item.childs && item.childs.length > 0) {
        filterSearchData(item.childs, text, arr)
      }
    })
    return arr
  }
  if (searchText.value) {
    const arr = filterSearchData(treeData.value, searchText.value, [])
    console.log('搜索结果:', arr)
    return arr
  }
  return treeData.value
})

// 展开全部
const expandAll = () => {
  const allKeys = getAllKeys(treeData.value)
  expandedKeys.value = allKeys
}

// 取消展开
const cancelExpand = () => {
  expandedKeys.value = []
}

// 获取所有节点的key
const getAllKeys = (data: any[]): string[] => {
  let keys: string[] = []
  data.forEach((item) => {
    keys.push(item.id)
    if (item.childs && item.childs.length > 0) {
      keys = keys.concat(getAllKeys(item.childs))
    }
  })
  return keys
}

// 勾选节点时的回调
const onCheck: TreeProps['onCheck'] = (checked, info) => {
  const checkedNodes = info.checkedNodes
  selectedDepartments.value = checkedNodes.map((node) => {
    return {
      id: node.id,
      department_name: node.department_name || node.name || '',
      type: node.type,
      company_name: node.company_name || node.full_name || '',
    }
  })
  setCheckByCheckKey(checkedKeys.value.checked, treeData.value)

  // 如果当前在搜索状态，也需要更新搜索结果的选中状态
  if (searchText.value) {
    updateFilterDataCheckStatus()
  }
}

// 移除已选择的部门
const removeSelected = (item: any) => {
  checkedKeys.value.checked = checkedKeys.value.checked.filter((key: any) => key !== item.id)
  selectedDepartments.value = selectedDepartments.value.filter((dep) => dep.id !== item.id)
}

const changeByFilter = (data: any) => {
  if (data.check) {
    if (!checkedKeys.value.checked.includes(data.id)) {
      checkedKeys.value.checked.push(data.id)
      selectedDepartments.value.push({
        id: data.id,
        department_name: data.department_name || data.name || '',
        type: data.type,
        company_name: data.company_name || data.full_name || '',
      })
    }
  } else {
    checkedKeys.value.checked = checkedKeys.value.checked.filter((key: any) => key !== data.id)
    selectedDepartments.value = selectedDepartments.value.filter((dep: any) => dep.id !== data.id)
  }

  // 更新树形数据中的选中状态
  setCheckByCheckKey(checkedKeys.value.checked, treeData.value)

  // 同时更新搜索结果中的选中状态
  if (searchText.value) {
    updateFilterDataCheckStatus()
  }
}

// 更新搜索结果中的选中状态
const updateFilterDataCheckStatus = () => {
  // 触发 filterData 的重新计算
  // 由于 filterData 是 computed，它会自动根据 checkedKeys 的变化重新计算
}

const openTableDrag = () => {
  const el: HTMLElement | null = document.getElementById('departments-drag')
  console.log(el)

  if (!el) return
  setTimeout(() => {
    new Sortable(el, {
      animation: 300,
      handle: '.drag',
      delay: 50,
      group: 'shared',
      onEnd: (item: any) => {
        const { oldIndex, newIndex } = item
        const currRow = selectedDepartments.value.splice(oldIndex, 1)[0]
        const arr = cloneDeep(selectedDepartments.value)
        selectedDepartments.value = []
        arr.splice(newIndex, 0, currRow)
        selectedDepartments.value = arr
      },
    })
  }, 100)
}

// const setCheckNull = (data: any) => {
//   data.forEach((item: any) => {
//     item.check = false
//     if (item.childs && item.childs.length > 0) {
//       setCheckNull(item.childs)
//     }
//   })
// }

// const handleClear = () => {
//   selectedDepartments.value = []
//   checkedKeys.value.checked = []
//   setCheckNull(treeData.value)
// }

// 确认选择
const handleOk = () => {
  emit('change', selectedDepartments.value)
  open.value = false
}
const setCheck = (ids: any, data: Array<any>) => {
  data.forEach((item: any) => {
    Object.assign(item, { check: false })
    if (ids.includes(item.id)) {
      item.check = true
    }
    if (item.childs && item.childs.length > 0) {
      setCheck(ids, item.childs)
    }
  })
}
// 取消选择
const handleCancel = () => {
  open.value = false
  emit('close')
}

// 打开对话框
const showModal = async (departments: any[], treeDataList: any[]) => {
  // 设置树数据
  treeData.value = treeDataList

  // 从树数据中查找完整的部门信息
  const findDeptInTree = (deptId: string, treeData: any[]): any => {
    for (const node of treeData) {
      if (String(node.id) === String(deptId)) {
        return node
      }
      if (node.childs && node.childs.length > 0) {
        const found = findDeptInTree(deptId, node.childs)
        if (found) return found
      }
    }
    return null
  }

  // 使用树数据中的完整信息来设置选中的部门
  selectedDepartments.value = departments.map((dept) => {
    const fullDeptInfo = findDeptInTree(dept.id, treeDataList)
    return {
      id: dept.id,
      department_name: dept.department_name || fullDeptInfo?.department_name || '',
      type: dept.type || fullDeptInfo?.type || 3,
      // 优先使用树数据中的完整路径信息
      company_name: fullDeptInfo?.company_name || fullDeptInfo?.full_name || dept.company_name || '',
    }
  })

  // 设置选中的keys
  checkedKeys.value.checked = departments.map((item) => item.id)

  setCheck(checkedKeys.value.checked, treeDataList)
  console.log('SelectDepart - 树数据:', treeData.value)
  console.log('SelectDepart - 选中部门:', selectedDepartments.value)

  // 展开所有节点（可选）
  expandedKeys.value = getAllKeys(treeDataList)
  open.value = true
  await nextTick()
  setTimeout(() => {
    console.log(123)
    openTableDrag()
  }, 100)
}

const setCheckByCheckKey = (keys: Array<any>, data: Array<any>) => {
  data.forEach((item: any) => {
    if (keys.includes(item.id)) {
      item.check = true
    } else {
      item.check = false
    }
    if (item.childs && item.childs.length > 0) {
      setCheckByCheckKey(keys, item.childs)
    }
  })
}

// 监听选中部门变化，初始化拖拽
// watch(
//   selectedDepartments.value,
//   () => {
//     console.log(123)

//   },
//   { immediate: true, deep: true },
// )

defineExpose({
  showModal,
})
</script>
<style lang="scss" scoped>
:deep(.ant-tree) {
  .ant-tree-treenode {
    padding: 4px 8px;

    &:hover {
      background-color: #f5f5f5;
    }

    .ant-tree-checkbox {
      align-self: auto;
      margin-top: 6px;
    }

    .ant-tree-node-content-wrapper {
      min-height: 24px;
      line-height: 24px;
    }

    .ant-tree-switcher-icon {
      padding-top: 6px;
      font-size: 14px;
      color: #c0c0c0;
    }
  }
}

:deep(.ant-modal-header) {
  padding: 16px 24px;
  margin: -24px -24px 24px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-modal-footer) {
  padding: 10px 16px;
  margin: 24px -24px -24px;
  border-top: 1px solid #f0f0f0;
}
</style>
